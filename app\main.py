"""
FastAPI application entry point for WhatsApp Hotel Bot MVP
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.logging import setup_logging, get_logger
from app.core.metrics import init_metrics
from app.api.v1.api import api_router
from app.database import init_db, close_db
from app.middleware.monitoring import (
    MonitoringMiddleware,
    HealthCheckMiddleware,
    SecurityHeadersMiddleware,
    RateLimitingMiddleware
)

# Setup logging
setup_logging()
logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting WhatsApp Hotel Bot application...")

    try:
        # Initialize database
        await init_db()
        logger.info("Database initialized")

        # Initialize metrics
        if settings.PROMETHEUS_ENABLED:
            init_metrics()
            logger.info("Metrics initialized")

        # Start Green API monitoring
        try:
            from app.services.green_api_monitoring import start_monitoring
            import asyncio
            monitoring_task = asyncio.create_task(start_monitoring())
            logger.info("Green API monitoring started")
        except Exception as e:
            logger.warning(f"Failed to start Green API monitoring: {e}")

        logger.info("Application startup completed")
        yield

    except Exception as e:
        logger.error(f"Application startup failed: {str(e)}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down application...")

        # Cancel monitoring task
        try:
            if 'monitoring_task' in locals():
                monitoring_task.cancel()
                logger.info("Green API monitoring stopped")
        except Exception as e:
            logger.warning(f"Error stopping monitoring: {e}")

        try:
            await close_db()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")
        logger.info("Application shutdown completed")

# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="MVP система WhatsApp-ботов для отелей с фокусом на управление отзывами и автоматизацию коммуникации",
    version=settings.VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware (order matters - last added is executed first)
# Security headers middleware
app.add_middleware(SecurityHeadersMiddleware)

# Rate limiting middleware
app.add_middleware(RateLimitingMiddleware, requests_per_minute=60)

# Health check middleware (for optimized health check logging)
app.add_middleware(HealthCheckMiddleware)

# Green API monitoring middleware
from app.middleware.green_api_middleware import GreenAPIMiddleware
app.add_middleware(GreenAPIMiddleware)

# User authentication middleware
from app.middleware.auth_middleware import AuthMiddleware
app.add_middleware(AuthMiddleware)

# Hotel tenant middleware (for multi-tenancy support)
from app.middleware.tenant_middleware import add_hotel_tenant_middlewares
add_hotel_tenant_middlewares(
    app,
    hotel_header="X-Hotel-ID",
    require_hotel=False,  # Not required for all endpoints
    excluded_paths=["/", "/health", "/docs", "/redoc", "/openapi.json", "/favicon.ico"],
    enable_permissions=True
)

# Monitoring middleware (should be early in the chain)
app.add_middleware(MonitoringMiddleware)

# CORS middleware (should be one of the last)
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "WhatsApp Hotel Bot API",
        "version": settings.VERSION,
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "whatsapp-hotel-bot",
        "version": settings.VERSION
    }

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc) if settings.DEBUG else "An error occurred"
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
